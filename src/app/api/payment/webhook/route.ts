import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';
import { headers } from 'next/headers';
import { getOrderById, getOrderByPaymentIntentIdAdmin, updateOrder } from '@/lib/firebase/admin-services/order-service';
import { getPaymentSettingsAdmin } from '@/lib/firebase/admin-services/settings-service';
import { getAdminFirestore } from '@/lib/firebase/admin';
import { FieldValue } from 'firebase-admin/firestore';

export async function POST(request: NextRequest) {
  try {
    // Get payment settings from Firestore
    const paymentSettings = await getPaymentSettingsAdmin();

    if (!paymentSettings?.stripeEnabled || !paymentSettings?.stripeSecretKey) {
      return NextResponse.json(
        { error: 'Stripe payments are not configured' },
        { status: 500 }
      );
    }

    // Initialize Stripe with the secret key
    const stripe = new Stripe(paymentSettings.stripeSecretKey, {
      apiVersion: '2025-04-30.basil', // this is the latest stable version for Stripe API version
    });

    // Get the signature from the headers
    const headersList = await headers();
    const signature = headersList.get('stripe-signature');

    if (!signature) {
      return NextResponse.json(
        { error: 'Missing stripe-signature header' },
        { status: 400 }
      );
    }

    // Get the raw body
    const rawBody = await request.text();

    // Verify the webhook signature
    const endpointSecret = process.env.STRIPE_WEBHOOK_SECRET;

    if (!endpointSecret) {
      return NextResponse.json(
        { error: 'Stripe webhook secret is not configured' },
        { status: 500 }
      );
    }

    let event;
    try {
      event = stripe.webhooks.constructEvent(rawBody, signature, endpointSecret);
    } catch (err) {
      console.error(`Webhook signature verification failed: ${err}`);
      return NextResponse.json(
        { error: 'Webhook signature verification failed' },
        { status: 400 }
      );
    }

    // Handle the event
    switch (event.type) {
      case 'payment_intent.succeeded': {
        const paymentIntent = event.data.object as any;
        await handlePaymentIntentSucceeded(paymentIntent);
        break;
      }
      case 'payment_intent.payment_failed': {
        const failedPaymentIntent = event.data.object as any;
        await handlePaymentIntentFailed(failedPaymentIntent);
        break;
      }
      default:
        console.log(`Unhandled event type ${event.type}`);
    }

    return NextResponse.json({ received: true });
  } catch (error) {
    console.error('Error handling webhook:', error);
    return NextResponse.json(
      { error: 'Failed to process webhook' },
      { status: 500 }
    );
  }
}

// Handle successful payment
async function handlePaymentIntentSucceeded(paymentIntent: any) {
  try {
    // Idempotency check - prevent duplicate processing
    const existingOrder = await getOrderByPaymentIntentIdAdmin(paymentIntent.id);
    if (existingOrder?.paymentStatus === 'paid') {
      console.log(`Webhook: Order for PaymentIntent ${paymentIntent.id} already processed.`);
      return;
    }

    // Get the internal order ID from metadata
    const { internalOrderId } = paymentIntent.metadata;

    if (!internalOrderId) {
      console.error(`Webhook Error: Missing internalOrderId in PaymentIntent metadata for ${paymentIntent.id}`);
      throw new Error('Missing internalOrderId in metadata');
    }

    // Fetch the pending order from Firestore
    const pendingOrder = await getOrderById(internalOrderId);

    if (!pendingOrder) {
      console.error(`Webhook Error: Order with internalOrderId ${internalOrderId} not found for PaymentIntent ${paymentIntent.id}`);
      throw new Error('Pending order not found');
    }

    // Update the order status to 'paid'
    await updateOrder(internalOrderId, {
      status: 'processing',
      paymentStatus: 'paid',
      paymentMethod: 'credit_card',
      updatedAt: new Date()
    });

    // Store payment details in a separate field or collection if needed
    // This is a workaround since these fields aren't in the Order type
    const db = getAdminFirestore();
    await db.collection('orders').doc(internalOrderId).update({
      stripePaymentIntentId: paymentIntent.id,
      stripeChargeId: paymentIntent.latest_charge,
      paidAt: FieldValue.serverTimestamp()
    });

    console.log(`Webhook: Order ${internalOrderId} successfully updated to paid for PaymentIntent ${paymentIntent.id}`);
  } catch (error) {
    console.error('Error handling successful payment:', error);
    throw error; // Re-throw to trigger a retry from Stripe
  }
}

// Handle failed payment
async function handlePaymentIntentFailed(paymentIntent: any) {
  try {
    // Get the internal order ID from metadata
    const { internalOrderId } = paymentIntent.metadata;

    if (internalOrderId) {
      // Fetch the pending order from Firestore
      const pendingOrder = await getOrderById(internalOrderId);

      if (pendingOrder) {
        // Update the order status to 'payment_failed'
        await updateOrder(internalOrderId, {
          paymentStatus: 'failed',
          status: 'cancelled',
          updatedAt: new Date()
        });

        // Store payment error details
        const db = getAdminFirestore();
        await db.collection('orders').doc(internalOrderId).update({
          paymentError: paymentIntent.last_payment_error?.message ?? 'Payment failed',
          failedAt: FieldValue.serverTimestamp()
        });

        console.log(`Webhook: Order ${internalOrderId} marked as failed for PaymentIntent ${paymentIntent.id}`);
      }
    }

    // Log the failed payment
    console.error(`Payment failed: ${paymentIntent.id}`, paymentIntent.last_payment_error);
  } catch (error) {
    console.error('Error handling failed payment:', error);
  }
}
